-- 反馈表创建SQL（用于测试）
CREATE TABLE `feedbacks` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `title` varchar(200) NOT NULL COMMENT '反馈标题',
  `content` text NOT NULL COMMENT '反馈内容',
  `type` varchar(20) DEFAULT 'suggestion' COMMENT '反馈类型',
  `status` varchar(20) DEFAULT 'pending' COMMENT '审核状态',
  `is_starred` tinyint(1) DEFAULT '0' COMMENT '是否标星',
  `is_public` tinyint(1) DEFAULT '0' COMMENT '是否公开显示',
  `admin_reply` text COMMENT '管理员回复',
  `admin_user_id` bigint unsigned DEFAULT NULL COMMENT '审核管理员ID',
  `view_count` int DEFAULT '0' COMMENT '查看次数',
  `like_count` int DEFAULT '0' COMMENT '点赞次数',
  `tags` varchar(255) DEFAULT NULL COMMENT '标签，多个标签用逗号分隔',
  `priority` int DEFAULT '0' COMMENT '优先级，数字越大优先级越高',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_feedbacks_deleted_at` (`deleted_at`),
  KEY `idx_feedbacks_user_id` (`user_id`),
  KEY `idx_feedbacks_status` (`status`),
  KEY `idx_feedbacks_type` (`type`),
  KEY `idx_feedbacks_is_starred` (`is_starred`),
  KEY `idx_feedbacks_is_public` (`is_public`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户反馈表';
