package router

import (
	"github.com/flipped-aurora/gin-vue-admin/server/router/aliyun"
	"github.com/flipped-aurora/gin-vue-admin/server/router/authing"
	"github.com/flipped-aurora/gin-vue-admin/server/router/clipboard"
	"github.com/flipped-aurora/gin-vue-admin/server/router/device"
	"github.com/flipped-aurora/gin-vue-admin/server/router/example"
	"github.com/flipped-aurora/gin-vue-admin/server/router/integral"
	"github.com/flipped-aurora/gin-vue-admin/server/router/llm"
	"github.com/flipped-aurora/gin-vue-admin/server/router/mcp"
	"github.com/flipped-aurora/gin-vue-admin/server/router/payment"
	"github.com/flipped-aurora/gin-vue-admin/server/router/product"
	"github.com/flipped-aurora/gin-vue-admin/server/router/system"
	"github.com/flipped-aurora/gin-vue-admin/server/router/translate"
	"github.com/flipped-aurora/gin-vue-admin/server/router/websocket"
	"github.com/flipped-aurora/gin-vue-admin/server/router/wecom"
)

var RouterGroupApp = new(RouterGroup)

type RouterGroup struct {
	System     system.RouterGroup
	Example    example.RouterGroup
	Integral   integral.RouterGroup
	Mcp        mcp.RouterGroup
	Authing    authing.RouterGroup
	Translate  translate.RouterGroup
	LLM        llm.RouterGroup
	Payment    payment.RouterGroup
	Product    product.RouterGroup
	Device     device.RouterGroup
	Clipboard  clipboard.RouterGroup
	WechatWork wecom.RouterGroup
	WebSocket  websocket.RouterGroup
	Aliyun     aliyun.RouterGroup
}
