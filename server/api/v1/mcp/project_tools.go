package mcp

import (
	"context"
	"encoding/json"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mcp"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mcp/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type ProjectToolsApi struct{}

// CreateProjectTools 创建projectTools表
// @Tags ProjectTools
// @Summary 创建projectTools表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body mcp.ProjectTools true "创建projectTools表"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /projectTools/createProjectTools [post]
func (projectToolsApi *ProjectToolsApi) CreateProjectTools(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var projectTools mcp.ProjectTools
	err := c.ShouldBindJSON(&projectTools)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = projectToolsService.CreateProjectTools(ctx, &projectTools)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}

	// 清除工具库查询缓存
	clearProjectToolsSearchCacheInternal(ctx)

	response.OkWithMessage("创建成功", c)
}

// DeleteProjectTools 删除projectTools表
// @Tags ProjectTools
// @Summary 删除projectTools表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body mcp.ProjectTools true "删除projectTools表"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /projectTools/deleteProjectTools [delete]
func (projectToolsApi *ProjectToolsApi) DeleteProjectTools(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ID := c.Query("ID")
	err := projectToolsService.DeleteProjectTools(ctx, ID)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}

	// 清除工具库查询缓存
	clearProjectToolsSearchCacheInternal(ctx)

	response.OkWithMessage("删除成功", c)
}

// DeleteProjectToolsByIds 批量删除projectTools表
// @Tags ProjectTools
// @Summary 批量删除projectTools表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /projectTools/deleteProjectToolsByIds [delete]
func (projectToolsApi *ProjectToolsApi) DeleteProjectToolsByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	IDs := c.QueryArray("IDs[]")
	err := projectToolsService.DeleteProjectToolsByIds(ctx, IDs)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}

	// 清除工具库查询缓存
	clearProjectToolsSearchCacheInternal(ctx)

	response.OkWithMessage("批量删除成功", c)
}

// UpdateProjectTools 更新projectTools表
// @Tags ProjectTools
// @Summary 更新projectTools表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body mcp.ProjectTools true "更新projectTools表"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /projectTools/updateProjectTools [put]
func (projectToolsApi *ProjectToolsApi) UpdateProjectTools(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var projectTools mcp.ProjectTools
	err := c.ShouldBindJSON(&projectTools)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = projectToolsService.UpdateProjectTools(ctx, projectTools)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}

	// 清除工具库查询缓存
	clearProjectToolsSearchCacheInternal(ctx)

	response.OkWithMessage("更新成功", c)
}

// FindProjectTools 用id查询projectTools表
// @Tags ProjectTools
// @Summary 用id查询projectTools表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param ID query uint true "用id查询projectTools表"
// @Success 200 {object} response.Response{data=mcp.ProjectTools,msg=string} "查询成功"
// @Router /projectTools/findProjectTools [get]
func (projectToolsApi *ProjectToolsApi) FindProjectTools(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ID := c.Query("ID")
	reprojectTools, err := projectToolsService.GetProjectTools(ctx, ID)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(reprojectTools, c)
}

// GetProjectToolsList 获取项目工具列表
// @Tags ProjectTools
// @Summary 获取项目工具列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.ProjectToolsSearch true "页码, 每页大小"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /projectTools/getProjectToolsList [get]
func (p *ProjectToolsApi) GetProjectToolsList(c *gin.Context) {
	var pageInfo request.ProjectToolsSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	list, total, err := projectToolsService.GetProjectToolsInfoList(c.Request.Context(), pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetProjectToolsDataSource 获取项目工具数据源
// @Tags ProjectTools
// @Summary 获取项目工具数据源
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=[]mcp.ProjectTools,msg=string} "获取成功"
// @Router /projectTools/getProjectToolsDataSource [get]
func (p *ProjectToolsApi) GetProjectToolsDataSource(c *gin.Context) {
	list, err := projectToolsService.GetProjectToolsDataSource(c.Request.Context())
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(list, "获取成功", c)
}

// GetProjectToolsPublic 不需要鉴权的projectTools表接口
// @Tags ProjectTools
// @Summary 不需要鉴权的projectTools表接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /projectTools/getProjectToolsPublic [get]
func (projectToolsApi *ProjectToolsApi) GetProjectToolsPublic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	projectToolsService.GetProjectToolsPublic(ctx)
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的projectTools表接口信息",
	}, "获取成功", c)
}

// UseTool 用户调用工具并扣除积分
// @Tags ProjectTools
// @Summary 用户调用工具并扣除积分
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param toolID query string true "工具ID"
// @Success 200 {object} response.Response{msg=string} "调用成功"
// @Router /projectTools/useTool [post]
func (projectToolsApi *ProjectToolsApi) UseTool(c *gin.Context) {
	toolID := c.Query("toolID")
	userID := utils.GetUserID(c)
	err := projectToolsService.UseToolWithPoints(c.Request.Context(), userID, toolID)
	if err != nil {
		global.GVA_LOG.Error("工具调用失败!", zap.Error(err))
		response.FailWithMessage("工具调用失败: "+err.Error(), c)
		return
	}
	response.OkWithMessage("调用成功，已扣除积分", c)
}

// GetAllProjectToolsList 获取所有未被删除且所属项目启用的工具，带 fullName 字段
func (projectToolsApi *ProjectToolsApi) GetAllProjectToolsList(c *gin.Context) {
	var list []mcp.ProjectTools

	// 获取 projectUUId 参数
	projectUUId := c.Query("projectUUId")
	// 获取平台筛选参数（mac/windows/linux），空表示全平台
	platform := c.Query("platform")

	db := global.GVA_DB.Joins("JOIN projects ON project_tools.project_uuid = projects.uuid").
		Where("project_tools.deleted_at IS NULL AND projects.deleted_at IS NULL AND projects.is_enabled = ? AND (project_tools.is_disabled = 0 OR project_tools.is_disabled IS NULL)", true)

	// 如果传了 projectUUId，则增加筛选条件
	if projectUUId != "" {
		db = db.Where("project_tools.project_uuid = ?", projectUUId)
	}

	// 平台筛选：当传入 platform 时，筛选 projects.supported_platforms 包含该平台，或平台字段为空/空数组（视为全平台）
	if platform != "" {
		db = db.Where("(projects.supported_platforms IS NULL OR projects.supported_platforms = '' OR projects.supported_platforms = '[]' OR JSON_CONTAINS(CAST(projects.supported_platforms AS JSON), ?) OR JSON_CONTAINS(CAST(JSON_UNQUOTE(CAST(projects.supported_platforms AS JSON)) AS JSON), ?))",
			"\""+platform+"\"", "\""+platform+"\"")
	}

	// 这里要用 db 变量
	err := db.Find(&list).Error
	if err != nil {
		global.GVA_LOG.Error("获取全部工具失败!", zap.Error(err))
		response.FailWithMessage("获取全部工具失败:"+err.Error(), c)
		return
	}

	// 构造带 fullName 字段的返回
	var result []gin.H
	for _, tool := range list {
		projectUUID := ""
		if tool.ProjectUUId != nil {
			projectUUID = *tool.ProjectUUId
		}
		name := ""
		if tool.Name != nil {
			name = *tool.Name
		}
		fullName := projectUUID + "--" + name
		item := gin.H{
			"ID":                  tool.ID,
			"projectUUId":         tool.ProjectUUId,
			"projectId":           tool.ProjectId,
			"name":                tool.Name,
			"description":         tool.Description,
			"descriptionChinese":  tool.DescriptionChinese,
			"inputSchema":         tool.InputSchema,
			"points":              tool.Points,
			"outputSchema":        tool.OutputSchema,
			"regex":               tool.Regex,
			"c_name":              tool.CName,
			"fullName":            fullName,
			"is_single_call":      tool.IsSingleCall,
			"supportedExtensions": tool.SupportedExtensions,
			"multiFileType":       tool.MultiFileType,
			"keywords":            tool.Keywords,
			"platforms":           tool.Platforms,
			"canHandleDirectory":  tool.CanHandleDirectory,
			"prerequisiteToolId":  tool.PrerequisiteToolId,
			"canDirectExecute":    tool.CanDirectExecute,
			"isDangerous":         tool.IsDangerous,
			"isDisabled":          tool.IsDisabled,
		}
		result = append(result, item)
	}
	response.OkWithDetailed(result, "获取成功", c)
}

// clearProjectToolsSearchCacheInternal 内部方法：清除工具库查询缓存并重新生成
func clearProjectToolsSearchCacheInternal(ctx context.Context) {
	if global.GVA_REDIS == nil {
		return // Redis未配置，跳过缓存清除
	}

	// 清除mcp-tools缓存
	cacheKey := "mcp-tools"
	err := global.GVA_REDIS.Del(ctx, cacheKey).Err()
	if err != nil {
		global.GVA_LOG.Error("自动清除mcp-tools缓存失败", zap.String("cacheKey", cacheKey), zap.Error(err))
	} else {
		global.GVA_LOG.Info("自动清除mcp-tools缓存成功", zap.String("cacheKey", cacheKey))
	}

	// 清除mcp-tools-grouped缓存
	groupedCacheKey := "mcp-tools-grouped"
	err = global.GVA_REDIS.Del(ctx, groupedCacheKey).Err()
	if err != nil {
		global.GVA_LOG.Error("自动清除mcp-tools-grouped缓存失败", zap.String("cacheKey", groupedCacheKey), zap.Error(err))
	} else {
		global.GVA_LOG.Info("自动清除mcp-tools-grouped缓存成功", zap.String("cacheKey", groupedCacheKey))
	}

	// 清除mcp-tools-simple缓存
	simpleCacheKey := "mcp-tools-simple"
	err = global.GVA_REDIS.Del(ctx, simpleCacheKey).Err()
	if err != nil {
		global.GVA_LOG.Error("自动清除mcp-tools-simple缓存失败", zap.String("cacheKey", simpleCacheKey), zap.Error(err))
	} else {
		global.GVA_LOG.Info("自动清除mcp-tools-simple缓存成功", zap.String("cacheKey", simpleCacheKey))
	}

	// 清除缓存后，异步重新生成缓存
	go func() {
		// 创建新的context用于异步操作
		asyncCtx := context.Background()

		// 重新生成所有缓存
		// 1. 生成分组格式数据
		var tools []mcp.ProjectTools
		db := global.GVA_DB.Joins("JOIN projects ON project_tools.project_uuid = projects.uuid").
			Where("project_tools.deleted_at IS NULL AND projects.deleted_at IS NULL AND projects.is_enabled = ? AND (project_tools.is_disabled = 0 OR project_tools.is_disabled IS NULL)", true)

		err := db.Find(&tools).Error
		if err != nil {
			global.GVA_LOG.Error("异步重新生成缓存时查询工具失败!", zap.Error(err))
			return
		}

		// 按项目UUID分组工具
		groupedTools := make(map[string][]interface{})

		for _, tool := range tools {
			projectUUID := ""
			if tool.ProjectUUId != nil {
				projectUUID = *tool.ProjectUUId
			}

			// 构造工具对象
			toolObj := map[string]interface{}{
				"id":                  tool.ID,
				"projectUUId":         tool.ProjectUUId,
				"projectId":           tool.ProjectId,
				"name":                tool.Name,
				"description":         tool.Description,
				"descriptionChinese":  tool.DescriptionChinese,
				"inputSchema":         tool.InputSchema,
				"points":              tool.Points,
				"outputSchema":        tool.OutputSchema,
				"regex":               tool.Regex,
				"c_name":              tool.CName,
				"is_single_call":      tool.IsSingleCall,
				"supportedExtensions": tool.SupportedExtensions,
				"multiFileType":       tool.MultiFileType,
				"keywords":            tool.Keywords,
				"platforms":           tool.Platforms,
				"canHandleDirectory":  tool.CanHandleDirectory,
				"prerequisiteToolId":  tool.PrerequisiteToolId,
				"canDirectExecute":    tool.CanDirectExecute,
				"isDangerous":         tool.IsDangerous,
				"isDisabled":          tool.IsDisabled,
			}

			// 添加到对应项目的工具数组中
			if projectUUID != "" {
				groupedTools[projectUUID] = append(groupedTools[projectUUID], toolObj)
			}
		}

		// 2. 生成文本格式数据与简化文本
		results, err := projectsService.ProjectsSearch(asyncCtx, nil)
		if err != nil {
			global.GVA_LOG.Error("异步重新生成缓存时查询项目失败!", zap.Error(err))
			return
		}
		formattedText := FormatProjectsToToolLibrary(results)
		simpleText := FormatProjectsToSimpleList(results)

		// 3. 同时保存两个缓存到Redis
		if global.GVA_REDIS != nil {
			// 保存分组缓存
			groupedCacheKey := "mcp-tools-grouped"
			groupedToolsJSON, err := json.Marshal(groupedTools)
			if err == nil {
				global.GVA_REDIS.Set(asyncCtx, groupedCacheKey, string(groupedToolsJSON), 0).Err()
				global.GVA_LOG.Info("异步重新生成mcp-tools-grouped缓存成功")
			}

			// 保存文本缓存
			textCacheKey := "mcp-tools"
			global.GVA_REDIS.Set(asyncCtx, textCacheKey, formattedText, 0).Err()
			global.GVA_LOG.Info("异步重新生成mcp-tools缓存成功")

			// 保存简化文本缓存
			simpleCacheKey := "mcp-tools-simple"
			global.GVA_REDIS.Set(asyncCtx, simpleCacheKey, simpleText, 0).Err()
			global.GVA_LOG.Info("异步重新生成mcp-tools-simple缓存成功")
		}
	}()
}
