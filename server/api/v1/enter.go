package v1

import (
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/aliyun"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/authing"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/clipboard"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/device"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/example"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/integral"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/llm"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/mcp"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/payment"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/product"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/system"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/translate"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/websocket"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/wecom"
)

var ApiGroupApp = new(ApiGroup)

type ApiGroup struct {
	SystemApiGroup     system.ApiGroup
	ExampleApiGroup    example.ApiGroup
	IntegralApiGroup   integral.ApiGroup
	McpApiGroup        mcp.ApiGroup
	AuthingApiGroup    authing.ApiGroup
	TranslateApiGroup  translate.ApiGroup
	LLMApiGroup        llm.ApiGroup
	PaymentApiGroup    payment.ApiGroup
	ProductApiGroup    product.ApiGroup
	DeviceApiGroup     device.ApiGroup
	ClipboardApiGroup  clipboard.ApiGroup
	WechatWorkApiGroup wecom.ApiGroup
	WebSocketApiGroup  websocket.ApiGroup
	AliyunApiGroup     aliyun.ApiGroup
}
