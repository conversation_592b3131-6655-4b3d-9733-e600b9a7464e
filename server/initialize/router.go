package initialize

import (
	"fmt"
	"net/http"
	"os"

	"github.com/flipped-aurora/gin-vue-admin/server/docs"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/flipped-aurora/gin-vue-admin/server/router"
	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

type justFilesFilesystem struct {
	fs http.FileSystem
}

func (fs justFilesFilesystem) Open(name string) (http.File, error) {
	f, err := fs.fs.Open(name)
	if err != nil {
		return nil, err
	}

	stat, err := f.Stat()
	if stat.IsDir() {
		return nil, os.ErrPermission
	}

	return f, nil
}

// 初始化总路由

func Routers() *gin.Engine {
	Router := gin.New()
	Router.Use(gin.Recovery())
	if gin.Mode() == gin.DebugMode {
		Router.Use(gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
			// 过滤掉根路径的 404 日志
			if param.Path == "/" && param.StatusCode == 404 {
				return ""
			}
			return fmt.Sprintf("[GIN] %v | %3d | %13v | %15s | %-7s %#v\n",
				param.TimeStamp.Format("2006/01/02 - 15:04:05"),
				param.StatusCode,
				param.Latency,
				param.ClientIP,
				param.Method,
				param.Path,
			)
		}))
	}

	systemRouter := router.RouterGroupApp.System
	exampleRouter := router.RouterGroupApp.Example
	// 如果想要不使用nginx代理前端网页，可以修改 web/.env.production 下的
	// VUE_APP_BASE_API = /
	// VUE_APP_BASE_PATH = http://localhost
	// 然后执行打包命令 npm run build。在打开下面3行注释
	// Router.Static("/favicon.ico", "./dist/favicon.ico")
	// Router.Static("/assets", "./dist/assets")   // dist里面的静态资源
	// Router.StaticFile("/", "./dist/index.html") // 前端网页入口页面

	Router.StaticFS(global.GVA_CONFIG.Local.StorePath, justFilesFilesystem{http.Dir(global.GVA_CONFIG.Local.StorePath)}) // Router.Use(middleware.LoadTls())  // 如果需要使用https 请打开此中间件 然后前往 core/server.go 将启动模式 更变为 Router.RunTLS("端口","你的cre/pem文件","你的key文件")
	// 跨域，如需跨域可以打开下面的注释
	Router.Use(middleware.Cors())        // 直接放行全部跨域请求
	Router.Use(middleware.CorsByRules()) // 按照配置的规则放行跨域请求
	global.GVA_LOG.Info("use middleware cors")
	docs.SwaggerInfo.BasePath = global.GVA_CONFIG.System.RouterPrefix
	Router.GET(global.GVA_CONFIG.System.RouterPrefix+"/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
	global.GVA_LOG.Info("register swagger handler")
	// 方便统一添加路由组前缀 多服务器上线使用

	PublicGroup := Router.Group(global.GVA_CONFIG.System.RouterPrefix)
	PrivateGroup := Router.Group(global.GVA_CONFIG.System.RouterPrefix)

	PrivateGroup.Use(middleware.JWTAuth()).Use(middleware.CasbinHandler())

	{
		// 健康监测 - 保持原有的简单接口
		PublicGroup.GET("/health", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"status":  "ok",
				"message": "service is healthy",
			})
		})
	}
	{
		systemRouter.InitBaseRouter(PublicGroup)                       // 注册基础功能路由 不做鉴权
		systemRouter.InitWechatOAuthRouter(PublicGroup)                // 注册微信OAuth路由
		systemRouter.InitInitRouter(PublicGroup)                       // 自动初始化相关
		systemRouter.InitAuthProviderRouter(PrivateGroup, PublicGroup) // 注册认证提供商路由（公开）
		systemRouter.InitHeartbeatRouter(PublicGroup)                  // 注册心跳检测路由 不做鉴权
	}

	{
		systemRouter.InitApiRouter(PrivateGroup, PublicGroup)                 // 注册功能api路由
		systemRouter.InitJwtRouter(PrivateGroup, PublicGroup)                 // jwt相关路由
		systemRouter.InitUserRouter(PrivateGroup, PublicGroup)                // 注册用户路由
		systemRouter.InitMenuRouter(PrivateGroup)                             // 注册menu路由
		systemRouter.InitSystemRouter(PrivateGroup)                           // system相关路由
		systemRouter.InitCasbinRouter(PrivateGroup)                           // 权限相关路由
		systemRouter.InitAutoCodeRouter(PrivateGroup, PublicGroup)            // 创建自动化代码
		systemRouter.InitAuthorityRouter(PrivateGroup)                        // 注册角色路由
		systemRouter.InitSysDictionaryRouter(PrivateGroup)                    // 字典管理
		systemRouter.InitAutoCodeHistoryRouter(PrivateGroup)                  // 自动化代码历史
		systemRouter.InitSysOperationRecordRouter(PrivateGroup)               // 操作记录
		systemRouter.InitSysDictionaryDetailRouter(PrivateGroup, PublicGroup) // 字典详情管理
		systemRouter.InitAuthorityBtnRouterRouter(PrivateGroup)               // 按钮权限管理
		systemRouter.InitSysExportTemplateRouter(PrivateGroup, PublicGroup)   // 导出模板
		systemRouter.InitSysParamsRouter(PrivateGroup, PublicGroup)           // 参数管理
		systemRouter.InitUserSessionRouter(PrivateGroup, PublicGroup)
		systemRouter.InitUserGeneralSettingsRouter(PrivateGroup)                 // 用户通用设置路由
		systemRouter.InitDesktopSettingsRouter(PrivateGroup)                     // 桌面设置路由
		exampleRouter.InitCustomerRouter(PrivateGroup)                           // 客户路由
		exampleRouter.InitFileUploadAndDownloadRouter(PrivateGroup, PublicGroup) // 文件上传下载功能路由（含公开上传接口）
		exampleRouter.InitAttachmentCategoryRouterRouter(PrivateGroup)           // 文件上传下载分类

	}

	//插件路由安装
	InstallPlugin(PrivateGroup, PublicGroup, Router)

	// 注册业务路由
	initBizRouter(PrivateGroup, PublicGroup)

	global.GVA_ROUTERS = Router.Routes()

	global.GVA_LOG.Info("router register success")
	return Router
}
